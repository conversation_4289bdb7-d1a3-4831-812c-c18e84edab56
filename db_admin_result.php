<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询结果 - 数据库管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .query-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 4px 4px 0;
        }
        
        .query-sql {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 1rem 0;
        }
        
        .table-container {
            overflow-x: auto;
            max-height: 70vh;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            white-space: nowrap;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .table tbody tr:nth-child(even) {
            background: #fafafa;
        }
        
        .table tbody tr:nth-child(even):hover {
            background: #f0f0f0;
        }
        
        .result-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .empty-result {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-result-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .cell-content {
            cursor: pointer;
            position: relative;
        }
        
        .cell-content:hover {
            background: #e3f2fd;
            border-radius: 2px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table th,
            .table td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
            
            .result-stats {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 查询结果</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 
            <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>">
                <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?>
            </a> > 
            <a href="?action=query&db=<?= htmlspecialchars($this->selectedDb) ?>">SQL查询</a> > 
            查询结果
        </div>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <div>
                <h2>📈 查询执行结果</h2>
            </div>
            <div>
                <a href="?action=query&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-primary">
                    📝 新建查询
                </a>
                <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-secondary">
                    ⬅️ 返回表列表
                </a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                执行的SQL语句
            </div>
            <div class="card-body">
                <div class="query-sql"><?= htmlspecialchars($_POST['sql'] ?? '') ?></div>
            </div>
        </div>
        
        <?php if (isset($result) && $result): ?>
            <?php 
                $fields = [];
                $rows = [];
                $rowCount = 0;
                
                if (!$result->EOF) {
                    $fields = array_keys($result->fields);
                    
                    // 收集所有数据行
                    while (!$result->EOF && $rowCount < 100) {
                        $rows[] = $result->fields;
                        $result->MoveNext();
                        $rowCount++;
                    }
                }
            ?>
            
            <div class="success-message">
                ✅ 查询执行成功！
            </div>
            
            <?php if ($rowCount > 0): ?>
                <div class="query-info">
                    <strong>📊 结果统计:</strong> 返回 <?= $rowCount ?> 行数据，共 <?= count($fields) ?> 个字段
                    <?php if ($rowCount >= 100): ?>
                        <br><strong>⚠️ 注意:</strong> 结果已限制为前100行
                    <?php endif; ?>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        查询结果数据
                    </div>
                    
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">#</th>
                                    <?php foreach ($fields as $field): ?>
                                        <th><?= htmlspecialchars($field) ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($rows as $index => $row): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <?php foreach ($fields as $field): ?>
                                            <td>
                                                <div class="cell-content" onclick="showCellContent(this, '<?= htmlspecialchars(addslashes($row[$field] ?? '')) ?>')">
                                                    <?php 
                                                        $value = $row[$field] ?? '';
                                                        if (is_null($value)) {
                                                            echo '<em style="color: #999;">NULL</em>';
                                                        } elseif ($value === '') {
                                                            echo '<em style="color: #999;">(空)</em>';
                                                        } else {
                                                            echo htmlspecialchars(mb_strlen($value) > 50 ? mb_substr($value, 0, 50) . '...' : $value);
                                                        }
                                                    ?>
                                                </div>
                                            </td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="result-stats">
                        <span>显示 <?= $rowCount ?> 行结果</span>
                        <span>执行时间: <?= number_format((microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))) * 1000, 2) ?> ms</span>
                    </div>
                </div>
                
            <?php else: ?>
                <div class="card">
                    <div class="empty-result">
                        <div class="empty-result-icon">📭</div>
                        <h3>查询无结果</h3>
                        <p>查询执行成功，但没有返回任何数据</p>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php endif; ?>
    </div>
    
    <!-- 单元格内容弹窗 -->
    <div id="cellModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; max-width: 80%; max-height: 80%; overflow: auto;">
            <h3>单元格内容</h3>
            <hr style="margin: 1rem 0;">
            <pre id="cellContent" style="white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow: auto; background: #f8f9fa; padding: 1rem; border-radius: 4px;"></pre>
            <div style="margin-top: 1rem; text-align: right;">
                <button onclick="closeCellModal()" class="btn btn-secondary">关闭</button>
            </div>
        </div>
    </div>
    
    <script>
        function showCellContent(element, content) {
            document.getElementById('cellContent').textContent = content || '(空)';
            document.getElementById('cellModal').style.display = 'block';
        }
        
        function closeCellModal() {
            document.getElementById('cellModal').style.display = 'none';
        }
        
        // 点击模态框背景关闭
        document.getElementById('cellModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCellModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCellModal();
            }
        });
    </script>
</body>
</html>
