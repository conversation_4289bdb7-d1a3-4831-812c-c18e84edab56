<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL查询 - 数据库管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            resize: vertical;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .query-examples {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .example-query {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example-query:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .security-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #856404;
        }
        
        .security-warning h4 {
            margin-bottom: 0.5rem;
            color: #856404;
        }
        
        .security-list {
            list-style: none;
            padding: 0;
        }
        
        .security-list li {
            padding: 0.25rem 0;
        }
        
        .security-list li:before {
            content: "⚠️";
            margin-right: 0.5rem;
        }
        
        .query-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .db-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 4px 4px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .query-actions {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 SQL查询</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 
            <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>">
                <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?>
            </a> > 
            SQL查询
        </div>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <div>
                <h2>🔍 执行SQL查询</h2>
            </div>
            <div>
                <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-secondary">
                    ⬅️ 返回表列表
                </a>
            </div>
        </div>
        
        <div class="db-info">
            <strong>📊 当前数据库:</strong> <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?> 
            (<?= htmlspecialchars($this->selectedDb) ?>)
        </div>
        
        <div class="card">
            <div class="card-header">
                安全提示
            </div>
            <div class="card-body">
                <div class="security-warning">
                    <h4>🔒 安全限制</h4>
                    <ul class="security-list">
                        <li>只允许执行 SELECT 查询语句</li>
                        <li>禁止 INSERT、UPDATE、DELETE、DROP 等修改操作</li>
                        <li>查询结果限制为最多100行</li>
                        <li>所有输入都经过安全验证</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                SQL查询编辑器
            </div>
            <div class="card-body">
                <form method="POST" action="?action=execute&db=<?= htmlspecialchars($this->selectedDb) ?>">
                    <div class="form-group">
                        <label class="form-label" for="sql">SQL语句:</label>
                        <textarea 
                            class="form-control" 
                            id="sql" 
                            name="sql" 
                            rows="8" 
                            placeholder="输入您的SELECT查询语句..."
                            required
                        ><?= htmlspecialchars($_POST['sql'] ?? '') ?></textarea>
                    </div>
                    
                    <div class="query-actions">
                        <button type="submit" class="btn btn-success">
                            ▶️ 执行查询
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearQuery()">
                            🗑️ 清空
                        </button>
                        <button type="button" class="btn btn-primary" onclick="formatQuery()">
                            ✨ 格式化
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                查询示例
            </div>
            <div class="card-body">
                <p>点击下面的示例查询可以快速填入编辑器：</p>
                <div class="query-examples">
                    <div class="example-query" onclick="setQuery('SELECT * FROM table_name LIMIT 10')">
                        SELECT * FROM table_name LIMIT 10
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT COUNT(*) as total FROM table_name')">
                        SELECT COUNT(*) as total FROM table_name
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT column1, column2 FROM table_name WHERE condition ORDER BY column1')">
                        SELECT column1, column2 FROM table_name WHERE condition ORDER BY column1
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT DISTINCT column_name FROM table_name')">
                        SELECT DISTINCT column_name FROM table_name
                    </div>
                    <div class="example-query" onclick="setQuery('SELECT * FROM table_name WHERE column LIKE \'%search%\'')">
                        SELECT * FROM table_name WHERE column LIKE '%search%'
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function setQuery(query) {
            document.getElementById('sql').value = query;
            document.getElementById('sql').focus();
        }
        
        function clearQuery() {
            document.getElementById('sql').value = '';
            document.getElementById('sql').focus();
        }
        
        function formatQuery() {
            const textarea = document.getElementById('sql');
            let sql = textarea.value.trim();
            
            if (!sql) return;
            
            // 简单的SQL格式化
            sql = sql.replace(/\s+/g, ' '); // 合并多个空格
            sql = sql.replace(/\bSELECT\b/gi, 'SELECT');
            sql = sql.replace(/\bFROM\b/gi, '\nFROM');
            sql = sql.replace(/\bWHERE\b/gi, '\nWHERE');
            sql = sql.replace(/\bORDER BY\b/gi, '\nORDER BY');
            sql = sql.replace(/\bGROUP BY\b/gi, '\nGROUP BY');
            sql = sql.replace(/\bHAVING\b/gi, '\nHAVING');
            sql = sql.replace(/\bLIMIT\b/gi, '\nLIMIT');
            sql = sql.replace(/\bJOIN\b/gi, '\nJOIN');
            sql = sql.replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN');
            sql = sql.replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN');
            sql = sql.replace(/\bINNER JOIN\b/gi, '\nINNER JOIN');
            
            textarea.value = sql;
        }
        
        // 添加快捷键支持
        document.getElementById('sql').addEventListener('keydown', function(e) {
            // Ctrl+Enter 执行查询
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.form.submit();
            }
            
            // Tab键插入制表符而不是跳转焦点
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;
                this.value = this.value.substring(0, start) + '\t' + this.value.substring(end);
                this.selectionStart = this.selectionEnd = start + 1;
            }
        });
        
        // 自动调整文本框高度
        document.getElementById('sql').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.max(200, this.scrollHeight) + 'px';
        });
    </script>
</body>
</html>
