<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - 数据库管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .error-container {
            text-align: center;
            padding: 3rem 2rem;
        }
        
        .error-icon {
            font-size: 6rem;
            color: #dc3545;
            margin-bottom: 2rem;
        }
        
        .error-title {
            font-size: 2rem;
            color: #dc3545;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f5c6cb;
            font-weight: 600;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .error-details {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #dc3545;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            text-decoration: none;
            color: white;
        }
        
        .action-buttons {
            margin-top: 2rem;
        }
        
        .help-section {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .help-section h4 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .help-list {
            list-style: none;
            padding: 0;
        }
        
        .help-list li {
            padding: 0.25rem 0;
        }
        
        .help-list li:before {
            content: "💡";
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .error-container {
                padding: 2rem 1rem;
            }
            
            .error-icon {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .action-buttons {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>❌ 操作错误</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 错误信息
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-title">操作失败</div>
                <div class="error-message">
                    很抱歉，在执行您的请求时遇到了错误。请查看下面的详细信息并尝试解决问题。
                </div>
                
                <div class="action-buttons">
                    <a href="javascript:history.back()" class="btn btn-secondary">
                        ⬅️ 返回上一页
                    </a>
                    <a href="?" class="btn btn-primary">
                        🏠 返回首页
                    </a>
                    <?php if (isset($this->selectedDb) && $this->selectedDb): ?>
                        <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>" class="btn btn-success">
                            📋 查看表列表
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                错误详情
            </div>
            <div class="card-body">
                <div class="error-details"><?= htmlspecialchars($message) ?></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                故障排除建议
            </div>
            <div class="card-body">
                <div class="help-section">
                    <h4>🔍 常见问题解决方案</h4>
                    <ul class="help-list">
                        <li>检查数据库连接是否正常</li>
                        <li>确认表名和字段名拼写正确</li>
                        <li>验证SQL语句语法是否正确</li>
                        <li>确保您有足够的数据库访问权限</li>
                        <li>检查是否使用了被禁止的SQL操作</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>🛡️ 安全限制提醒</h4>
                    <ul class="help-list">
                        <li>只允许执行SELECT查询语句</li>
                        <li>禁止修改数据的操作（INSERT、UPDATE、DELETE等）</li>
                        <li>表名和字段名必须符合命名规范</li>
                        <li>查询结果限制为最多100行</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>📞 需要帮助？</h4>
                    <ul class="help-list">
                        <li>检查项目的数据库配置文件</li>
                        <li>确认数据库服务是否正在运行</li>
                        <li>联系系统管理员获取技术支持</li>
                        <li>查看服务器错误日志获取更多信息</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
