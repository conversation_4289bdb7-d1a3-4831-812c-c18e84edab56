<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表结构 - <?= htmlspecialchars($this->selectedTable) ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            text-decoration: none;
            color: white;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 4px;
            text-transform: uppercase;
        }
        
        .badge-primary {
            background: #667eea;
            color: white;
        }
        
        .badge-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .badge-success {
            background: #28a745;
            color: white;
        }
        
        .badge-info {
            background: #17a2b8;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .table-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 4px 4px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏗️ 表结构</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 
            <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>">
                <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?>
            </a> > 
            <?= htmlspecialchars($this->selectedTable) ?>
        </div>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <div>
                <h2>📋 <?= htmlspecialchars($this->selectedTable) ?></h2>
            </div>
            <div>
                <a href="?action=browse&db=<?= htmlspecialchars($this->selectedDb) ?>&table=<?= htmlspecialchars($this->selectedTable) ?>" 
                   class="btn btn-success">
                    👁️ 浏览数据
                </a>
                <a href="?action=query&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-primary">
                    📝 SQL查询
                </a>
                <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-secondary">
                    ⬅️ 返回表列表
                </a>
            </div>
        </div>
        
        <?php if (!empty($structure)): ?>
            <div class="table-info">
                <strong>📊 表信息:</strong> 共 <?= count($structure) ?> 个字段
            </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                字段结构
            </div>
            
            <?php if (empty($structure)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">❌</div>
                    <h3>无法获取表结构</h3>
                    <p>可能是表不存在或没有访问权限</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>字段名</th>
                                <th>数据类型</th>
                                <th>长度</th>
                                <th>允许空值</th>
                                <th>默认值</th>
                                <th>其他属性</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($structure as $column): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($column->name) ?></strong>
                                        <?php if (isset($column->primary_key) && $column->primary_key): ?>
                                            <span class="badge badge-warning">PK</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            <?= htmlspecialchars($column->type) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (isset($column->max_length) && $column->max_length > 0): ?>
                                            <?= htmlspecialchars($column->max_length) ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($column->not_null) && $column->not_null): ?>
                                            <span class="badge badge-warning">NOT NULL</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">NULL</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($column->default_value) && $column->default_value !== null): ?>
                                            <code><?= htmlspecialchars($column->default_value) ?></code>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($column->auto_increment) && $column->auto_increment): ?>
                                            <span class="badge badge-info">AUTO_INCREMENT</span>
                                        <?php endif; ?>
                                        <?php if (isset($column->unique) && $column->unique): ?>
                                            <span class="badge badge-info">UNIQUE</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
