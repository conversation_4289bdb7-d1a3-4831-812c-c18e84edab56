<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .db-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .db-item {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        
        .db-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
            text-decoration: none;
            color: inherit;
        }
        
        .db-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .db-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .db-description {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .info-section {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        .info-section h3 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .db-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗄️ 数据库管理工具</h1>
        <p>基于项目现有连接机制的数据库管理界面</p>
    </div>
    
    <div class="container">
        <div class="card">
            <div class="card-header">
                选择数据库
            </div>
            <div class="card-body">
                <div class="db-grid">
                    <?php foreach ($databases as $dbKey): ?>
                        <?php $config = DatabaseConfig::$databases[$dbKey]; ?>
                        <a href="?action=tables&db=<?= htmlspecialchars($dbKey) ?>" class="db-item">
                            <div class="db-icon">🗃️</div>
                            <div class="db-name"><?= htmlspecialchars($config['description']) ?></div>
                            <div class="db-description">
                                数据库: <?= htmlspecialchars($config['name']) ?><br>
                                类型: <?= htmlspecialchars(strtoupper($config['type'])) ?>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                工具特性
            </div>
            <div class="card-body">
                <div class="info-section">
                    <h3>🔒 安全特性</h3>
                    <ul class="feature-list">
                        <li>防SQL注入保护</li>
                        <li>只读查询模式（禁止修改操作）</li>
                        <li>查询结果数量限制</li>
                        <li>表名和字段名验证</li>
                    </ul>
                </div>
                
                <div class="info-section">
                    <h3>🛠️ 功能特性</h3>
                    <ul class="feature-list">
                        <li>数据库表结构查看</li>
                        <li>表数据浏览和分页</li>
                        <li>自定义SQL查询执行</li>
                        <li>复用项目现有数据库连接</li>
                    </ul>
                </div>
                
                <div class="info-section">
                    <h3>💡 使用说明</h3>
                    <ul class="feature-list">
                        <li>点击上方数据库卡片开始使用</li>
                        <li>支持查看表结构和数据内容</li>
                        <li>可执行SELECT查询语句</li>
                        <li>所有操作都经过安全验证</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>数据库管理工具 - 基于 ADOdb 和项目现有连接机制构建</p>
        <p>⚠️ 仅供开发和调试使用，请勿在生产环境中暴露此工具</p>
    </div>
</body>
</html>
