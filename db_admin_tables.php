<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库表列表 - 数据库管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .table-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
            text-decoration: none;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            text-decoration: none;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .search-box {
            position: relative;
            max-width: 300px;
        }
        
        .search-input {
            width: 100%;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .search-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗃️ 数据库表列表</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 
            <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?>
        </div>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索表名..." id="tableSearch">
                <span class="search-icon">🔍</span>
            </div>
            <div>
                <a href="?action=query&db=<?= htmlspecialchars($this->selectedDb) ?>" class="btn btn-primary">
                    📝 SQL查询
                </a>
                <a href="?" class="btn btn-secondary">
                    🏠 返回首页
                </a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <span>数据库表 (共 <?= count($tables) ?> 个)</span>
                <span>数据库: <?= htmlspecialchars($this->selectedDb) ?></span>
            </div>
            
            <?php if (empty($tables)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">📭</div>
                    <h3>没有找到表</h3>
                    <p>当前数据库中没有表，或者没有访问权限</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table" id="tablesTable">
                        <thead>
                            <tr>
                                <th>表名</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tables as $table): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($table) ?></strong>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="?action=structure&db=<?= htmlspecialchars($this->selectedDb) ?>&table=<?= htmlspecialchars($table) ?>" 
                                               class="btn btn-info btn-sm">
                                                🏗️ 结构
                                            </a>
                                            <a href="?action=browse&db=<?= htmlspecialchars($this->selectedDb) ?>&table=<?= htmlspecialchars($table) ?>" 
                                               class="btn btn-success btn-sm">
                                                👁️ 浏览
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // 表格搜索功能
        document.getElementById('tableSearch').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('tablesTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const tableName = rows[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                if (tableName.includes(searchTerm)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
