<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览数据 - <?= htmlspecialchars($this->selectedTable) ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .breadcrumb {
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            text-decoration: none;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
            text-decoration: none;
            color: white;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-container {
            overflow-x: auto;
            max-height: 70vh;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            white-space: nowrap;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .table tbody tr:nth-child(even) {
            background: #fafafa;
        }
        
        .table tbody tr:nth-child(even):hover {
            background: #f0f0f0;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .data-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 4px 4px 0;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: #f8f9fa;
        }
        
        .page-info {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .cell-content {
            cursor: pointer;
            position: relative;
        }
        
        .cell-content:hover {
            background: #e3f2fd;
            border-radius: 2px;
        }
        
        .cell-tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table th,
            .table td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👁️ 浏览数据</h1>
        <div class="breadcrumb">
            <a href="?">首页</a> > 
            <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>">
                <?= htmlspecialchars(DatabaseConfig::$databases[$this->selectedDb]['description']) ?>
            </a> > 
            <?= htmlspecialchars($this->selectedTable) ?>
        </div>
    </div>
    
    <div class="container">
        <div class="toolbar">
            <div>
                <h2>📊 <?= htmlspecialchars($this->selectedTable) ?></h2>
            </div>
            <div>
                <a href="?action=structure&db=<?= htmlspecialchars($this->selectedDb) ?>&table=<?= htmlspecialchars($this->selectedTable) ?>" 
                   class="btn btn-info">
                    🏗️ 查看结构
                </a>
                <a href="?action=query&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-primary">
                    📝 SQL查询
                </a>
                <a href="?action=tables&db=<?= htmlspecialchars($this->selectedDb) ?>" 
                   class="btn btn-secondary">
                    ⬅️ 返回表列表
                </a>
            </div>
        </div>
        
        <?php if ($data && !$data->EOF): ?>
            <?php 
                $fields = array_keys($data->fields);
                $rowCount = 0;
                $rows = [];
                
                // 收集所有数据行
                while (!$data->EOF) {
                    $rows[] = $data->fields;
                    $data->MoveNext();
                    $rowCount++;
                }
            ?>
            
            <div class="data-info">
                <strong>📈 数据信息:</strong> 显示 <?= $rowCount ?> 行数据，共 <?= count($fields) ?> 个字段
                <br><strong>⚠️ 提示:</strong> 点击单元格查看完整内容，数据显示限制为前50行
            </div>
            
            <div class="card">
                <div class="card-header">
                    <span>表数据</span>
                    <span><?= $rowCount ?> 行</span>
                </div>
                
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 50px;">#</th>
                                <?php foreach ($fields as $field): ?>
                                    <th><?= htmlspecialchars($field) ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rows as $index => $row): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <?php foreach ($fields as $field): ?>
                                        <td>
                                            <div class="cell-content" onclick="showCellContent(this, '<?= htmlspecialchars(addslashes($row[$field] ?? '')) ?>')">
                                                <?php 
                                                    $value = $row[$field] ?? '';
                                                    if (is_null($value)) {
                                                        echo '<em style="color: #999;">NULL</em>';
                                                    } elseif ($value === '') {
                                                        echo '<em style="color: #999;">(空)</em>';
                                                    } else {
                                                        echo htmlspecialchars(mb_strlen($value) > 50 ? mb_substr($value, 0, 50) . '...' : $value);
                                                    }
                                                ?>
                                            </div>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <div class="page-info">
                        显示前 <?= min($rowCount, 50) ?> 行数据
                        <?php if ($rowCount >= 50): ?>
                            (可能还有更多数据，请使用SQL查询获取完整结果)
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
        <?php else: ?>
            <div class="card">
                <div class="empty-state">
                    <div class="empty-state-icon">📭</div>
                    <h3>没有数据</h3>
                    <p>表中没有数据或查询失败</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- 单元格内容弹窗 -->
    <div id="cellModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; max-width: 80%; max-height: 80%; overflow: auto;">
            <h3>单元格内容</h3>
            <hr style="margin: 1rem 0;">
            <pre id="cellContent" style="white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow: auto; background: #f8f9fa; padding: 1rem; border-radius: 4px;"></pre>
            <div style="margin-top: 1rem; text-align: right;">
                <button onclick="closeCellModal()" class="btn btn-secondary">关闭</button>
            </div>
        </div>
    </div>
    
    <script>
        function showCellContent(element, content) {
            document.getElementById('cellContent').textContent = content || '(空)';
            document.getElementById('cellModal').style.display = 'block';
        }
        
        function closeCellModal() {
            document.getElementById('cellModal').style.display = 'none';
        }
        
        // 点击模态框背景关闭
        document.getElementById('cellModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCellModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeCellModal();
            }
        });
    </script>
</body>
</html>
