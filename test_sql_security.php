<?php
/**
 * SQL安全检查测试工具
 * 测试修复后的安全验证是否正确工作
 */

// 模拟安全检查函数
function validateSqlSecurity($sql) {
    // 更精确的危险关键词检查 - 使用单词边界
    $dangerousPatterns = array(
        '/\bDROP\s+/i' => 'DROP',
        '/\bDELETE\s+/i' => 'DELETE', 
        '/\bUPDATE\s+/i' => 'UPDATE',
        '/\bINSERT\s+/i' => 'INSERT',
        '/\bALTER\s+/i' => 'ALTER',
        '/\bCREATE\s+/i' => 'CREATE',
        '/\bTRUNCATE\s+/i' => 'TRUNCATE',
        '/\bEXEC\s+/i' => 'EXEC',
        '/\bEXECUTE\s+/i' => 'EXECUTE',
        '/\bCALL\s+/i' => 'CALL',
        '/\bDECLARE\s+/i' => 'DECLARE',
        '/\bSET\s+/i' => 'SET',
        '/\bUSE\s+/i' => 'USE',
        '/\bGRANT\s+/i' => 'GRANT',
        '/\bREVOKE\s+/i' => 'REVOKE'
    );
    
    foreach ($dangerousPatterns as $pattern => $keyword) {
        if (preg_match($pattern, $sql)) {
            throw new Exception("为安全起见，禁止执行 $keyword 操作");
        }
    }
    
    if (!preg_match('/^\s*SELECT\s+/i', $sql)) {
        throw new Exception("只允许执行SELECT查询语句");
    }
    
    if (strpos($sql, ';') !== false && strpos($sql, ';') < strlen($sql) - 1) {
        throw new Exception("不允许执行多条SQL语句");
    }
    
    return true;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL安全检查测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: #28a745; color: white; padding: 2rem; border-radius: 8px; text-align: center; margin-bottom: 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #f8f9fa; padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; font-weight: 600; }
        .card-body { padding: 1.5rem; }
        .test-case { margin: 1rem 0; padding: 1rem; border-radius: 4px; }
        .test-pass { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-fail { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-sql { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 0.5rem; font-family: monospace; margin: 0.5rem 0; }
        .test-result { font-weight: bold; margin-top: 0.5rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 SQL安全检查测试</h1>
            <p>验证修复后的安全检查是否正确工作</p>
        </div>
        
        <div class="card">
            <div class="card-header">✅ 应该通过的查询（包含表名中的关键词）</div>
            <div class="card-body">
                <?php
                $valid_queries = array(
                    "SELECT * FROM user_table",
                    "SELECT * FROM house_info", 
                    "SELECT * FROM customer_data",
                    "SELECT name, address FROM user_profile",
                    "SELECT * FROM usage_statistics",
                    "SELECT id FROM cause_analysis",
                    "SELECT * FROM excuse_list",
                    "SELECT count(*) FROM user_settings",
                    "SELECT * FROM warehouse_inventory",
                    "SELECT * FROM purchase_orders"
                );
                
                foreach ($valid_queries as $sql) {
                    echo '<div class="test-case">';
                    echo '<div class="test-sql">' . htmlspecialchars($sql) . '</div>';
                    
                    try {
                        validateSqlSecurity($sql);
                        echo '<div class="test-case test-pass">';
                        echo '<div class="test-result">✅ 通过 - 查询被正确允许</div>';
                        echo '</div>';
                    } catch (Exception $e) {
                        echo '<div class="test-case test-fail">';
                        echo '<div class="test-result">❌ 失败 - ' . htmlspecialchars($e->getMessage()) . '</div>';
                        echo '</div>';
                    }
                    
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">❌ 应该被阻止的查询（真正的危险操作）</div>
            <div class="card-body">
                <?php
                $dangerous_queries = array(
                    "DROP TABLE users",
                    "DELETE FROM user_table",
                    "UPDATE user_table SET password = 'hack'",
                    "INSERT INTO users VALUES ('hacker', 'pass')",
                    "ALTER TABLE users ADD COLUMN hacked INT",
                    "CREATE TABLE malicious (id INT)",
                    "TRUNCATE TABLE important_data",
                    "USE master",
                    "EXEC sp_addlogin 'hacker'",
                    "CALL dangerous_procedure()",
                    "GRANT ALL ON users TO public",
                    "SET @var = 'malicious'"
                );
                
                foreach ($dangerous_queries as $sql) {
                    echo '<div class="test-case">';
                    echo '<div class="test-sql">' . htmlspecialchars($sql) . '</div>';
                    
                    try {
                        validateSqlSecurity($sql);
                        echo '<div class="test-case test-fail">';
                        echo '<div class="test-result">❌ 失败 - 危险查询被错误允许</div>';
                        echo '</div>';
                    } catch (Exception $e) {
                        echo '<div class="test-case test-pass">';
                        echo '<div class="test-result">✅ 通过 - ' . htmlspecialchars($e->getMessage()) . '</div>';
                        echo '</div>';
                    }
                    
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">🔍 边界情况测试</div>
            <div class="card-body">
                <?php
                $edge_cases = array(
                    "SELECT * FROM users WHERE name LIKE '%use%'",
                    "SELECT cause FROM issues",
                    "SELECT excuse FROM reasons", 
                    "SELECT * FROM house WHERE address LIKE '%update%'",
                    "SELECT customer_name FROM customers",
                    "SELECT * FROM user_table WHERE created_date > '2023-01-01'",
                    "select * from USER_PROFILES",  // 大小写测试
                    "  SELECT  *  FROM  user_data  ",  // 空格测试
                );
                
                foreach ($edge_cases as $sql) {
                    echo '<div class="test-case">';
                    echo '<div class="test-sql">' . htmlspecialchars($sql) . '</div>';
                    
                    try {
                        validateSqlSecurity($sql);
                        echo '<div class="test-case test-pass">';
                        echo '<div class="test-result">✅ 通过 - 边界情况正确处理</div>';
                        echo '</div>';
                    } catch (Exception $e) {
                        echo '<div class="test-case test-fail">';
                        echo '<div class="test-result">❌ 失败 - ' . htmlspecialchars($e->getMessage()) . '</div>';
                        echo '</div>';
                    }
                    
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">📊 测试总结</div>
            <div class="card-body">
                <p><strong>修复说明：</strong></p>
                <ul>
                    <li>使用正则表达式的单词边界 <code>\b</code> 来精确匹配SQL关键词</li>
                    <li>只有当关键词作为独立的SQL命令时才会被阻止</li>
                    <li>表名或字段名中包含关键词不会被误判</li>
                    <li>保持了原有的安全防护级别</li>
                </ul>
                
                <p><strong>技术细节：</strong></p>
                <ul>
                    <li><code>/\bUSE\s+/i</code> - 只匹配 "USE " 作为SQL命令</li>
                    <li><code>user_table</code> 中的 "use" 不会被匹配</li>
                    <li>大小写不敏感的匹配</li>
                    <li>要求关键词后面有空格</li>
                </ul>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">🚀 下一步</div>
            <div class="card-body">
                <p>如果所有测试都通过，您现在可以安全地访问包含关键词的表名了。</p>
                <p><strong>测试您的数据库管理工具：</strong></p>
                <ul>
                    <li><a href="db_manager.php">完整版数据库管理工具</a></li>
                    <li><a href="db_manager_simple.php">简化版数据库管理工具</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
