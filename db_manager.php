<?php
/**
 * 单文件数据库管理工具
 * 基于项目现有的数据库连接机制构建
 */

// 引入项目的ADOdb库
require_once('Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php');

// 数据库配置
class DatabaseConfig {
    public static $databases = [
        'ucity1' => [
            'name' => 'Ucity1',
            'user' => 'Program', 
            'password' => '',
            'type' => 'odbc',
            'description' => '站点数据库'
        ],
        'ucitycen' => [
            'name' => 'ucitycen',
            'user' => 'Program',
            'password' => '',
            'type' => 'odbc', 
            'description' => '中心数据库'
        ]
    ];
}

// 数据库管理类
class DatabaseManager {
    private $connection = null;
    private $currentDb = null;
    
    public function connect($dbKey) {
        if (!isset(DatabaseConfig::$databases[$dbKey])) {
            throw new Exception("未知的数据库配置: $dbKey");
        }
        
        $config = DatabaseConfig::$databases[$dbKey];
        
        try {
            $this->connection = ADONewConnection($config['type']);
            $result = $this->connection->PConnect($config['name'], $config['user'], $config['password']);
            
            if (!$result) {
                throw new Exception("数据库连接失败");
            }
            
            $this->connection->SetFetchMode(ADODB_FETCH_ASSOC);
            $this->currentDb = $dbKey;
            
            return true;
        } catch (Exception $e) {
            throw new Exception("连接数据库失败: " . $e->getMessage());
        }
    }
    
    public function getTables() {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        try {
            $tables = $this->connection->MetaTables('TABLES');
            return $tables ? $tables : [];
        } catch (Exception $e) {
            throw new Exception("获取表列表失败: " . $e->getMessage());
        }
    }
    
    public function getTableStructure($tableName) {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        
        try {
            $columns = $this->connection->MetaColumns($tableName);
            return $columns ? $columns : [];
        } catch (Exception $e) {
            throw new Exception("获取表结构失败: " . $e->getMessage());
        }
    }
    
    public function executeQuery($sql, $limit = 100) {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        $sql = trim($sql);
        if (empty($sql)) {
            throw new Exception("SQL语句不能为空");
        }
        
        $this->validateSqlSecurity($sql);
        
        try {
            if (stripos($sql, 'LIMIT') === false) {
                $sql .= " LIMIT $limit";
            }
            
            $result = $this->connection->Execute($sql);
            
            if (!$result) {
                throw new Exception("查询执行失败: " . $this->connection->ErrorMsg());
            }
            
            return $result;
        } catch (Exception $e) {
            throw new Exception("查询执行错误: " . $e->getMessage());
        }
    }
    
    private function validateSqlSecurity($sql) {
        $upperSql = strtoupper($sql);
        
        $dangerousKeywords = [
            'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE',
            'EXEC', 'EXECUTE', 'CALL', 'DECLARE', 'SET', 'USE', 'GRANT', 'REVOKE'
        ];
        
        foreach ($dangerousKeywords as $keyword) {
            if (strpos($upperSql, $keyword) !== false) {
                throw new Exception("为安全起见，禁止执行 $keyword 操作");
            }
        }
        
        if (!preg_match('/^\s*SELECT\s+/i', $sql)) {
            throw new Exception("只允许执行SELECT查询语句");
        }
        
        if (strpos($sql, ';') !== false && strpos($sql, ';') < strlen($sql) - 1) {
            throw new Exception("不允许执行多条SQL语句");
        }
    }
    
    public function getTableData($tableName, $limit = 50) {
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        
        $sql = "SELECT * FROM $tableName LIMIT $limit";
        return $this->executeQuery($sql, $limit);
    }
    
    public function close() {
        if ($this->connection) {
            $this->connection->Close();
            $this->connection = null;
            $this->currentDb = null;
        }
    }
    
    public function getCurrentDatabase() {
        return $this->currentDb;
    }
}

// 处理请求
$action = $_GET['action'] ?? 'home';
$selectedDb = $_GET['db'] ?? '';
$selectedTable = $_GET['table'] ?? '';
$error = null;
$result = null;

$dbManager = new DatabaseManager();

try {
    if ($selectedDb) {
        $dbManager->connect($selectedDb);
    }
    
    switch ($action) {
        case 'tables':
            $tables = $dbManager->getTables();
            break;
        case 'structure':
            $structure = $dbManager->getTableStructure($selectedTable);
            break;
        case 'browse':
            $data = $dbManager->getTableData($selectedTable);
            break;
        case 'execute':
            $sql = $_POST['sql'] ?? '';
            $result = $dbManager->executeQuery($sql);
            break;
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { font-size: 1.8rem; font-weight: 300; }
        .breadcrumb { margin-top: 0.5rem; opacity: 0.9; }
        .breadcrumb a { color: white; text-decoration: none; }
        .breadcrumb a:hover { text-decoration: underline; }
        .container { max-width: 1400px; margin: 2rem auto; padding: 0 2rem; }
        .toolbar { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 1rem 1.5rem; margin-bottom: 2rem; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem; }
        .btn { padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 0.5rem; font-size: 0.9rem; transition: all 0.3s ease; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; text-decoration: none; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #5a6268; text-decoration: none; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; text-decoration: none; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; text-decoration: none; color: white; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 2rem; }
        .card-header { background: #f8f9fa; padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #495057; }
        .card-body { padding: 1.5rem; }
        .table-container { overflow-x: auto; max-height: 70vh; }
        .table { width: 100%; border-collapse: collapse; font-size: 0.9rem; }
        .table th, .table td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #e9ecef; white-space: nowrap; max-width: 200px; overflow: hidden; text-overflow: ellipsis; }
        .table th { background: #f8f9fa; font-weight: 600; color: #495057; position: sticky; top: 0; z-index: 10; }
        .table tbody tr:hover { background: #f8f9fa; }
        .table tbody tr:nth-child(even) { background: #fafafa; }
        .db-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-top: 1rem; }
        .db-item { border: 2px solid #e9ecef; border-radius: 8px; padding: 1.5rem; text-align: center; transition: all 0.3s ease; cursor: pointer; text-decoration: none; color: inherit; }
        .db-item:hover { border-color: #667eea; transform: translateY(-2px); box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2); text-decoration: none; color: inherit; }
        .db-icon { font-size: 3rem; color: #667eea; margin-bottom: 1rem; }
        .db-name { font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; color: #333; }
        .db-description { color: #6c757d; font-size: 0.9rem; }
        .error-alert { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .success-alert { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .form-group { margin-bottom: 1.5rem; }
        .form-label { display: block; margin-bottom: 0.5rem; font-weight: 600; color: #495057; }
        .form-control { width: 100%; padding: 0.75rem; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.9rem; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; resize: vertical; }
        .form-control:focus { outline: none; border-color: #667eea; box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }
        .empty-state { text-align: center; padding: 3rem; color: #6c757d; }
        .empty-state-icon { font-size: 4rem; margin-bottom: 1rem; }
        .badge { display: inline-block; padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: 600; border-radius: 4px; text-transform: uppercase; }
        .badge-primary { background: #667eea; color: white; }
        .badge-warning { background: #ffc107; color: #212529; }
        .badge-success { background: #28a745; color: white; }
        .badge-info { background: #17a2b8; color: white; }
        .cell-content { cursor: pointer; position: relative; }
        .cell-content:hover { background: #e3f2fd; border-radius: 2px; }
        @media (max-width: 768px) { .container { padding: 0 1rem; } .header { padding: 1rem; } .toolbar { flex-direction: column; align-items: stretch; } .db-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗄️ 数据库管理工具</h1>
        <div class="breadcrumb">
            <a href="?">首页</a>
            <?php if ($selectedDb): ?>
                > <a href="?action=tables&db=<?= htmlspecialchars($selectedDb) ?>"><?= htmlspecialchars(DatabaseConfig::$databases[$selectedDb]['description']) ?></a>
            <?php endif; ?>
            <?php if ($selectedTable): ?>
                > <?= htmlspecialchars($selectedTable) ?>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="error-alert">
                <strong>❌ 错误:</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'home'): ?>
            <!-- 首页 -->
            <div class="card">
                <div class="card-header">选择数据库</div>
                <div class="card-body">
                    <div class="db-grid">
                        <?php foreach (DatabaseConfig::$databases as $dbKey => $config): ?>
                            <a href="?action=tables&db=<?= htmlspecialchars($dbKey) ?>" class="db-item">
                                <div class="db-icon">🗃️</div>
                                <div class="db-name"><?= htmlspecialchars($config['description']) ?></div>
                                <div class="db-description">
                                    数据库: <?= htmlspecialchars($config['name']) ?><br>
                                    类型: <?= htmlspecialchars(strtoupper($config['type'])) ?>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
        <?php elseif ($action === 'tables'): ?>
            <!-- 表列表 -->
            <div class="toolbar">
                <div><h2>📋 数据库表列表</h2></div>
                <div>
                    <a href="?action=query&db=<?= htmlspecialchars($selectedDb) ?>" class="btn btn-primary">📝 SQL查询</a>
                    <a href="?" class="btn btn-secondary">🏠 返回首页</a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">数据库表 (共 <?= isset($tables) ? count($tables) : 0 ?> 个)</div>
                <?php if (isset($tables) && !empty($tables)): ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr><th>表名</th><th>操作</th></tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables as $table): ?>
                                    <tr>
                                        <td><strong><?= htmlspecialchars($table) ?></strong></td>
                                        <td>
                                            <a href="?action=structure&db=<?= htmlspecialchars($selectedDb) ?>&table=<?= htmlspecialchars($table) ?>" class="btn btn-info" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">🏗️ 结构</a>
                                            <a href="?action=browse&db=<?= htmlspecialchars($selectedDb) ?>&table=<?= htmlspecialchars($table) ?>" class="btn btn-success" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">👁️ 浏览</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">📭</div>
                        <h3>没有找到表</h3>
                        <p>当前数据库中没有表，或者没有访问权限</p>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($action === 'structure'): ?>
            <!-- 表结构 -->
            <div class="toolbar">
                <div><h2>🏗️ <?= htmlspecialchars($selectedTable) ?> - 表结构</h2></div>
                <div>
                    <a href="?action=browse&db=<?= htmlspecialchars($selectedDb) ?>&table=<?= htmlspecialchars($selectedTable) ?>" class="btn btn-success">👁️ 浏览数据</a>
                    <a href="?action=tables&db=<?= htmlspecialchars($selectedDb) ?>" class="btn btn-secondary">⬅️ 返回表列表</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">字段结构</div>
                <?php if (isset($structure) && !empty($structure)): ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr><th>字段名</th><th>数据类型</th><th>长度</th><th>允许空值</th><th>默认值</th></tr>
                            </thead>
                            <tbody>
                                <?php foreach ($structure as $column): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($column->name) ?></strong>
                                            <?php if (isset($column->primary_key) && $column->primary_key): ?>
                                                <span class="badge badge-warning">PK</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><span class="badge badge-primary"><?= htmlspecialchars($column->type) ?></span></td>
                                        <td><?= isset($column->max_length) && $column->max_length > 0 ? htmlspecialchars($column->max_length) : '-' ?></td>
                                        <td>
                                            <?php if (isset($column->not_null) && $column->not_null): ?>
                                                <span class="badge badge-warning">NOT NULL</span>
                                            <?php else: ?>
                                                <span class="badge badge-success">NULL</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= isset($column->default_value) && $column->default_value !== null ? '<code>' . htmlspecialchars($column->default_value) . '</code>' : '-' ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">❌</div>
                        <h3>无法获取表结构</h3>
                        <p>可能是表不存在或没有访问权限</p>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($action === 'browse'): ?>
            <!-- 浏览数据 -->
            <div class="toolbar">
                <div><h2>👁️ <?= htmlspecialchars($selectedTable) ?> - 浏览数据</h2></div>
                <div>
                    <a href="?action=structure&db=<?= htmlspecialchars($selectedDb) ?>&table=<?= htmlspecialchars($selectedTable) ?>" class="btn btn-info">🏗️ 查看结构</a>
                    <a href="?action=tables&db=<?= htmlspecialchars($selectedDb) ?>" class="btn btn-secondary">⬅️ 返回表列表</a>
                </div>
            </div>

            <?php if (isset($data) && $data && !$data->EOF): ?>
                <?php
                    $fields = array_keys($data->fields);
                    $rowCount = 0;
                    $rows = [];

                    while (!$data->EOF && $rowCount < 50) {
                        $rows[] = $data->fields;
                        $data->MoveNext();
                        $rowCount++;
                    }
                ?>

                <div class="success-alert">
                    <strong>📈 数据信息:</strong> 显示 <?= $rowCount ?> 行数据，共 <?= count($fields) ?> 个字段
                </div>

                <div class="card">
                    <div class="card-header">表数据</div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">#</th>
                                    <?php foreach ($fields as $field): ?>
                                        <th><?= htmlspecialchars($field) ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($rows as $index => $row): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <?php foreach ($fields as $field): ?>
                                            <td>
                                                <div class="cell-content" onclick="alert('<?= htmlspecialchars(addslashes($row[$field] ?? '')) ?>')">
                                                    <?php
                                                        $value = $row[$field] ?? '';
                                                        if (is_null($value)) {
                                                            echo '<em style="color: #999;">NULL</em>';
                                                        } elseif ($value === '') {
                                                            echo '<em style="color: #999;">(空)</em>';
                                                        } else {
                                                            echo htmlspecialchars(mb_strlen($value) > 50 ? mb_substr($value, 0, 50) . '...' : $value);
                                                        }
                                                    ?>
                                                </div>
                                            </td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="empty-state">
                        <div class="empty-state-icon">📭</div>
                        <h3>没有数据</h3>
                        <p>表中没有数据或查询失败</p>
                    </div>
                </div>
            <?php endif; ?>

        <?php elseif ($action === 'query' || $action === 'execute'): ?>
            <!-- SQL查询 -->
            <div class="toolbar">
                <div><h2>📝 SQL查询</h2></div>
                <div>
                    <a href="?action=tables&db=<?= htmlspecialchars($selectedDb) ?>" class="btn btn-secondary">⬅️ 返回表列表</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">安全提示</div>
                <div class="card-body">
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 1rem; color: #856404;">
                        <strong>🔒 安全限制:</strong> 只允许执行 SELECT 查询语句，禁止修改操作，查询结果限制为最多100行
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">SQL查询编辑器</div>
                <div class="card-body">
                    <form method="POST" action="?action=execute&db=<?= htmlspecialchars($selectedDb) ?>">
                        <div class="form-group">
                            <label class="form-label" for="sql">SQL语句:</label>
                            <textarea class="form-control" id="sql" name="sql" rows="8" placeholder="输入您的SELECT查询语句..." required><?= htmlspecialchars($_POST['sql'] ?? '') ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-success">▶️ 执行查询</button>
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('sql').value=''; document.getElementById('sql').focus();">🗑️ 清空</button>
                    </form>
                </div>
            </div>

            <?php if ($action === 'execute' && isset($result)): ?>
                <?php if ($result && !$result->EOF): ?>
                    <?php
                        $fields = array_keys($result->fields);
                        $rowCount = 0;
                        $rows = [];

                        while (!$result->EOF && $rowCount < 100) {
                            $rows[] = $result->fields;
                            $result->MoveNext();
                            $rowCount++;
                        }
                    ?>

                    <div class="success-alert">
                        ✅ 查询执行成功！返回 <?= $rowCount ?> 行数据，共 <?= count($fields) ?> 个字段
                    </div>

                    <div class="card">
                        <div class="card-header">查询结果</div>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <?php foreach ($fields as $field): ?>
                                            <th><?= htmlspecialchars($field) ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($rows as $index => $row): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <?php foreach ($fields as $field): ?>
                                                <td>
                                                    <div class="cell-content" onclick="alert('<?= htmlspecialchars(addslashes($row[$field] ?? '')) ?>')">
                                                        <?php
                                                            $value = $row[$field] ?? '';
                                                            if (is_null($value)) {
                                                                echo '<em style="color: #999;">NULL</em>';
                                                            } elseif ($value === '') {
                                                                echo '<em style="color: #999;">(空)</em>';
                                                            } else {
                                                                echo htmlspecialchars(mb_strlen($value) > 50 ? mb_substr($value, 0, 50) . '...' : $value);
                                                            }
                                                        ?>
                                                    </div>
                                                </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="success-alert">
                        ✅ 查询执行成功，但没有返回数据
                    </div>
                <?php endif; ?>
            <?php endif; ?>

        <?php endif; ?>
    </div>

    <script>
        // Tab键插入制表符
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('sql');
            if (textarea) {
                textarea.addEventListener('keydown', function(e) {
                    if (e.key === 'Tab') {
                        e.preventDefault();
                        const start = this.selectionStart;
                        const end = this.selectionEnd;
                        this.value = this.value.substring(0, start) + '\t' + this.value.substring(end);
                        this.selectionStart = this.selectionEnd = start + 1;
                    }

                    // Ctrl+Enter 执行查询
                    if (e.ctrlKey && e.key === 'Enter') {
                        e.preventDefault();
                        this.form.submit();
                    }
                });
            }
        });
    </script>
</body>
</html>

<?php
// 清理资源
$dbManager->close();
?>
