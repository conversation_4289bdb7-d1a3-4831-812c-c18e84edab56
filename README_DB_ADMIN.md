# 数据库管理工具

基于项目现有数据库连接机制构建的PHP数据库管理工具，类似于Adminer的功能。

## 项目分析结果

### 数据库连接机制
通过分析项目代码，发现该项目使用了以下数据库连接方式：

1. **数据库配置**：
   - 站点数据库：`ucity1` (Ucity1)
   - 中心数据库：`ucitycen` (ucitycen)
   - 用户名：`Program`
   - 密码：空字符串
   - 连接类型：ODBC

2. **连接函数**：
   - 传统方式：`dbcs_mapdb()`, `dbcs_unmapdb()`, `dbcs_createshareinfo()`
   - 新版本：`kwcr2_mapdb()`, `kwcr2_unmapdb()`
   - ADOdb方式：使用ADOdb库的ODBC驱动

3. **ADOdb库位置**：
   - 路径：`Netask/BrowserUI/MessageCenter/adodb/`
   - 主文件：`adodb.inc.php`

## 工具特性

### 🔒 安全特性
- **防SQL注入保护**：多层安全验证机制
- **只读查询模式**：禁止所有修改操作（INSERT、UPDATE、DELETE等）
- **查询结果限制**：最多返回100行数据
- **表名验证**：严格的表名和字段名格式验证
- **多语句防护**：禁止执行多条SQL语句
- **注入模式检测**：自动检测常见的SQL注入模式

### 🛠️ 功能特性
- **数据库浏览**：查看可用的数据库列表
- **表结构查看**：详细的表字段信息显示
- **数据浏览**：分页浏览表数据内容
- **SQL查询执行**：安全的SELECT查询执行界面
- **响应式设计**：支持桌面和移动设备
- **实时搜索**：表名快速搜索功能

## 文件结构

```
db_admin.php              # 主应用文件
db_admin_home.php         # 首页模板
db_admin_tables.php       # 表列表页面
db_admin_structure.php    # 表结构页面
db_admin_browse.php       # 数据浏览页面
db_admin_query.php        # SQL查询界面
db_admin_result.php       # 查询结果页面
db_admin_error.php        # 错误显示页面
README_DB_ADMIN.md        # 使用说明文档
```

## 安装和使用

### 1. 部署文件
将所有文件放置在项目的Web可访问目录中。

### 2. 访问工具
在浏览器中访问：`http://your-domain/path/db_admin.php`

### 3. 选择数据库
- 点击首页的数据库卡片选择要管理的数据库
- 支持的数据库：站点数据库(ucity1)和中心数据库(ucitycen)

### 4. 浏览数据
- **查看表列表**：显示数据库中的所有表
- **查看表结构**：显示表的字段信息、数据类型等
- **浏览表数据**：分页显示表中的数据内容
- **执行查询**：编写和执行自定义的SELECT查询

## 安全限制

### 禁止的操作
- `DROP` - 删除表或数据库
- `DELETE` - 删除数据
- `UPDATE` - 更新数据
- `INSERT` - 插入数据
- `ALTER` - 修改表结构
- `CREATE` - 创建表或数据库
- `TRUNCATE` - 清空表
- `EXEC/EXECUTE` - 执行存储过程
- `CALL` - 调用函数
- `GRANT/REVOKE` - 权限管理

### 允许的操作
- `SELECT` - 查询数据（唯一允许的操作）

### 其他限制
- 查询结果最多100行
- 不允许执行多条SQL语句
- 自动检测和阻止SQL注入攻击
- 表名和字段名必须符合标准命名规范

## 使用示例

### 基本查询
```sql
SELECT * FROM table_name LIMIT 10
```

### 条件查询
```sql
SELECT column1, column2 FROM table_name WHERE condition ORDER BY column1
```

### 统计查询
```sql
SELECT COUNT(*) as total FROM table_name
```

### 模糊查询
```sql
SELECT * FROM table_name WHERE column LIKE '%search%'
```

## 技术实现

### 数据库连接
- 复用项目现有的ADOdb库
- 使用ODBC连接方式
- 支持持久连接以提高性能

### 安全机制
- 多层SQL验证
- 正则表达式模式匹配
- 输入参数过滤和转义
- 错误信息安全处理

### 用户界面
- 响应式Bootstrap风格设计
- 现代化的卡片式布局
- 直观的导航和操作流程
- 移动设备友好

## 注意事项

⚠️ **重要安全提醒**：
1. 此工具仅供开发和调试使用
2. 请勿在生产环境中暴露此工具
3. 建议添加访问控制和身份验证
4. 定期检查和更新安全机制

## 故障排除

### 常见问题
1. **连接失败**：检查数据库服务是否运行
2. **权限错误**：确认数据库用户权限
3. **表不存在**：验证表名拼写和大小写
4. **查询超时**：简化查询条件或添加索引

### 错误日志
查看服务器错误日志获取详细的错误信息。

## 扩展功能

可以考虑添加的功能：
- 数据导出（CSV、Excel）
- 查询历史记录
- 收藏夹查询
- 表关系图显示
- 索引信息查看

## 版本信息

- 版本：1.0.0
- 基于：ADOdb数据库抽象层
- 兼容：PHP 5.6+
- 数据库：支持ODBC连接的数据库

---

**开发说明**：此工具基于对项目现有数据库连接机制的深入分析构建，确保与项目环境的完全兼容性。
