<?php
/**
 * ADOdb库路径检测工具
 * 帮助找到系统中的ADOdb库文件
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADOdb库路径检测</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: #17a2b8; color: white; padding: 2rem; border-radius: 8px; text-align: center; margin-bottom: 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #f8f9fa; padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; font-weight: 600; }
        .card-body { padding: 1.5rem; }
        .success { border-left: 4px solid #28a745; }
        .success .card-header { background: #d4edda; color: #155724; }
        .error { border-left: 4px solid #dc3545; }
        .error .card-header { background: #f8d7da; color: #721c24; }
        .warning { border-left: 4px solid #ffc107; }
        .warning .card-header { background: #fff3cd; color: #856404; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 1rem; font-family: 'Consolas', 'Monaco', monospace; font-size: 0.9rem; white-space: pre-wrap; }
        .path-list { list-style: none; padding: 0; }
        .path-list li { padding: 0.5rem; margin: 0.25rem 0; border-radius: 4px; }
        .path-found { background: #d4edda; color: #155724; }
        .path-not-found { background: #f8d7da; color: #721c24; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 0.25rem; }
        .btn:hover { background: #138496; text-decoration: none; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 ADOdb库路径检测</h1>
            <p>自动搜索系统中的ADOdb库文件</p>
        </div>
        
        <?php
        // 搜索路径列表
        $search_paths = array(
            // 项目原始路径
            'Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php',
            
            // 常见的相对路径
            'IncludeFile/adodb/adodb.inc.php',
            'adodb/adodb.inc.php',
            'lib/adodb/adodb.inc.php',
            'includes/adodb/adodb.inc.php',
            'vendor/adodb/adodb-php/adodb.inc.php',
            
            // 相对于当前目录的路径
            '../adodb/adodb.inc.php',
            '../../adodb/adodb.inc.php',
            '../../../adodb/adodb.inc.php',
            
            // 相对于脚本目录的路径
            dirname(__FILE__) . '/adodb/adodb.inc.php',
            dirname(__FILE__) . '/../adodb/adodb.inc.php',
            dirname(__FILE__) . '/../../adodb/adodb.inc.php',
            
            // 系统常见路径
            '/usr/share/php/adodb/adodb.inc.php',
            '/usr/local/lib/php/adodb/adodb.inc.php',
            'C:/php/includes/adodb/adodb.inc.php',
            'C:/xampp/php/includes/adodb/adodb.inc.php'
        );
        
        $found_paths = array();
        $not_found_paths = array();
        
        foreach ($search_paths as $path) {
            if (file_exists($path)) {
                $found_paths[] = $path;
            } else {
                $not_found_paths[] = $path;
            }
        }
        
        // 显示找到的路径
        if (!empty($found_paths)) {
            echo '<div class="card success">';
            echo '<div class="card-header">✅ 找到ADOdb库文件</div>';
            echo '<div class="card-body">';
            echo '<p>在以下位置找到了ADOdb库文件：</p>';
            echo '<ul class="path-list">';
            foreach ($found_paths as $path) {
                $realpath = realpath($path);
                echo '<li class="path-found">';
                echo '<strong>路径:</strong> ' . htmlspecialchars($path) . '<br>';
                echo '<strong>实际路径:</strong> ' . htmlspecialchars($realpath) . '<br>';
                echo '<strong>文件大小:</strong> ' . number_format(filesize($path)) . ' 字节';
                echo '</li>';
            }
            echo '</ul>';
            
            // 提供配置代码
            $first_found = $found_paths[0];
            echo '<h4>配置代码：</h4>';
            echo '<p>将以下代码添加到您的PHP文件中：</p>';
            echo '<div class="code">$ADODB_PATH = \'' . addslashes($first_found) . '\';
require_once($ADODB_PATH);</div>';
            
            echo '</div></div>';
        } else {
            echo '<div class="card error">';
            echo '<div class="card-header">❌ 未找到ADOdb库文件</div>';
            echo '<div class="card-body">';
            echo '<p>在所有搜索路径中都未找到ADOdb库文件。</p>';
            echo '</div></div>';
        }
        
        // 显示未找到的路径
        if (!empty($not_found_paths)) {
            echo '<div class="card warning">';
            echo '<div class="card-header">⚠️ 搜索的路径</div>';
            echo '<div class="card-body">';
            echo '<p>以下路径中未找到ADOdb库文件：</p>';
            echo '<ul class="path-list">';
            foreach ($not_found_paths as $path) {
                echo '<li class="path-not-found">❌ ' . htmlspecialchars($path) . '</li>';
            }
            echo '</ul>';
            echo '</div></div>';
        }
        
        // 递归搜索功能
        function searchAdobFiles($dir, $maxDepth = 3, $currentDepth = 0) {
            $results = array();
            
            if ($currentDepth >= $maxDepth || !is_dir($dir) || !is_readable($dir)) {
                return $results;
            }
            
            try {
                $files = scandir($dir);
                foreach ($files as $file) {
                    if ($file === '.' || $file === '..') continue;
                    
                    $fullPath = $dir . DIRECTORY_SEPARATOR . $file;
                    
                    if (is_file($fullPath) && $file === 'adodb.inc.php') {
                        $results[] = $fullPath;
                    } elseif (is_dir($fullPath)) {
                        $results = array_merge($results, searchAdobFiles($fullPath, $maxDepth, $currentDepth + 1));
                    }
                }
            } catch (Exception $e) {
                // 忽略权限错误
            }
            
            return $results;
        }
        
        // 执行递归搜索
        echo '<div class="card">';
        echo '<div class="card-header">🔍 递归搜索结果</div>';
        echo '<div class="card-body">';
        echo '<p>在当前目录及其子目录中搜索adodb.inc.php文件...</p>';
        
        $recursive_results = searchAdobFiles('.', 4);
        
        if (!empty($recursive_results)) {
            echo '<p><strong>找到以下文件：</strong></p>';
            echo '<ul class="path-list">';
            foreach ($recursive_results as $path) {
                $realpath = realpath($path);
                echo '<li class="path-found">';
                echo '<strong>路径:</strong> ' . htmlspecialchars($path) . '<br>';
                echo '<strong>实际路径:</strong> ' . htmlspecialchars($realpath);
                echo '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p>在当前目录树中未找到adodb.inc.php文件。</p>';
        }
        echo '</div></div>';
        ?>
        
        <div class="card">
            <div class="card-header">💡 解决方案</div>
            <div class="card-body">
                <h4>如果未找到ADOdb库，您可以：</h4>
                <ol>
                    <li><strong>下载ADOdb库</strong>
                        <ul>
                            <li>官方网站: <a href="https://adodb.org/" target="_blank">https://adodb.org/</a></li>
                            <li>GitHub: <a href="https://github.com/ADOdb/ADOdb" target="_blank">https://github.com/ADOdb/ADOdb</a></li>
                        </ul>
                    </li>
                    <li><strong>解压到项目目录</strong>
                        <div class="code">解压后将adodb文件夹放到项目根目录
或者放到 lib/adodb/ 目录下</div>
                    </li>
                    <li><strong>使用Composer安装</strong>
                        <div class="code">composer require adodb/adodb-php</div>
                    </li>
                    <li><strong>检查原项目</strong>
                        <p>根据分析，原项目的ADOdb库应该在：</p>
                        <div class="code">Netask/BrowserUI/MessageCenter/adodb/</div>
                        <p>请检查该目录是否存在，或者库文件是否被移动。</p>
                    </li>
                </ol>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">📋 环境信息</div>
            <div class="card-body">
                <div class="code">PHP版本: <?= PHP_VERSION ?>
当前工作目录: <?= htmlspecialchars(getcwd()) ?>
脚本位置: <?= htmlspecialchars(__FILE__) ?>
操作系统: <?= PHP_OS ?>
ODBC扩展: <?= extension_loaded('odbc') ? '✅ 已安装' : '❌ 未安装' ?>
</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">🚀 下一步</div>
            <div class="card-body">
                <p>找到ADOdb库后，您可以使用以下工具：</p>
                <a href="db_manager_simple.php" class="btn">简化版数据库管理工具</a>
                <a href="db_manager.php" class="btn">完整版数据库管理工具</a>
                <a href="test_db_connection.php" class="btn">数据库连接测试</a>
            </div>
        </div>
    </div>
</body>
</html>
