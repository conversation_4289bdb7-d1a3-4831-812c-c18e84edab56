<?php
/**
 * 数据库连接测试脚本
 * 用于验证数据库管理工具的连接配置是否正确
 */

// 引入ADOdb库
require_once('Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php');

// 测试配置 - PHP 5.2兼容
$testConfigs = array(
    'ucity1' => array(
        'name' => 'Ucity1',
        'user' => 'Program',
        'password' => '',
        'type' => 'odbc',
        'description' => '站点数据库'
    ),
    'ucitycen' => array(
        'name' => 'ucitycen',
        'user' => 'Program',
        'password' => '',
        'type' => 'odbc',
        'description' => '中心数据库'
    )
);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-result {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .test-header {
            padding: 1rem 1.5rem;
            font-weight: 600;
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-body {
            padding: 1.5rem;
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .success .test-header {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
        
        .error .test-header {
            background: #f8d7da;
            color: #721c24;
        }
        
        .info {
            border-left: 4px solid #17a2b8;
        }
        
        .info .test-header {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .test-details {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 1rem;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 数据库连接测试</h1>
            <p>验证数据库管理工具的连接配置</p>
        </div>
        
        <?php
        // 测试ADOdb库是否可用
        if (!function_exists('ADONewConnection')) {
            echo '<div class="test-result error">';
            echo '<div class="test-header">❌ ADOdb库测试失败</div>';
            echo '<div class="test-body">';
            echo '<p>无法找到ADOdb库，请检查路径是否正确。</p>';
            echo '<div class="test-details">路径: Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php</div>';
            echo '</div></div>';
        } else {
            echo '<div class="test-result success">';
            echo '<div class="test-header">✅ ADOdb库加载成功</div>';
            echo '<div class="test-body">';
            echo '<p>ADOdb库已成功加载，版本信息如下：</p>';
            echo '<div class="test-details">';
            echo 'ADOdb版本: ' . (defined('ADODB_VERSION') ? ADODB_VERSION : '未知') . "\n";
            echo 'PHP版本: ' . PHP_VERSION . "\n";
            echo 'ADOdb目录: ' . ADODB_DIR;
            echo '</div></div></div>';
        }
        
        // 测试每个数据库配置
        foreach ($testConfigs as $dbKey => $config) {
            echo '<div class="test-result info">';
            echo '<div class="test-header">🗄️ 测试数据库: ' . htmlspecialchars($config['description']) . '</div>';
            echo '<div class="test-body">';
            
            try {
                $conn = ADONewConnection($config['type']);
                
                if (!$conn) {
                    throw new Exception("无法创建数据库连接对象");
                }
                
                echo '<p><strong>连接参数:</strong></p>';
                echo '<div class="test-details">';
                echo '数据库名: ' . htmlspecialchars($config['name']) . "\n";
                echo '用户名: ' . htmlspecialchars($config['user']) . "\n";
                echo '密码: ' . (empty($config['password']) ? '(空)' : '***') . "\n";
                echo '类型: ' . htmlspecialchars($config['type']);
                echo '</div>';
                
                // 尝试连接
                $result = $conn->PConnect($config['name'], $config['user'], $config['password']);
                
                if ($result) {
                    echo '<p style="color: #28a745;"><strong>✅ 连接成功！</strong></p>';
                    
                    // 尝试获取表列表
                    try {
                        $tables = $conn->MetaTables('TABLES');
                        if ($tables && count($tables) > 0) {
                            echo '<p>找到 ' . count($tables) . ' 个表：</p>';
                            echo '<div class="test-details">';
                            echo implode(', ', array_slice($tables, 0, 10));
                            if (count($tables) > 10) {
                                echo '... (还有 ' . (count($tables) - 10) . ' 个表)';
                            }
                            echo '</div>';
                        } else {
                            echo '<p style="color: #ffc107;">⚠️ 数据库中没有找到表</p>';
                        }
                    } catch (Exception $e) {
                        echo '<p style="color: #ffc107;">⚠️ 无法获取表列表: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    }
                    
                    $conn->Close();
                } else {
                    throw new Exception("连接失败: " . $conn->ErrorMsg());
                }
                
            } catch (Exception $e) {
                echo '<p style="color: #dc3545;"><strong>❌ 连接失败</strong></p>';
                echo '<div class="test-details">错误信息: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            echo '</div></div>';
        }
        ?>
        
        <div class="test-result info">
            <div class="test-header">📋 测试完成</div>
            <div class="test-body">
                <p>数据库连接测试已完成。如果所有测试都通过，您可以开始使用数据库管理工具。</p>
                <a href="db_admin.php" class="btn">🚀 启动数据库管理工具</a>
            </div>
        </div>
        
        <div class="test-result">
            <div class="test-header">🔍 故障排除</div>
            <div class="test-body">
                <p><strong>如果连接失败，请检查以下项目：</strong></p>
                <ul>
                    <li>确认数据库服务正在运行</li>
                    <li>检查ODBC数据源配置</li>
                    <li>验证数据库用户权限</li>
                    <li>确认防火墙设置</li>
                    <li>检查PHP的ODBC扩展是否已启用</li>
                </ul>
                
                <p><strong>PHP扩展检查：</strong></p>
                <div class="test-details">
                <?php
                $extensions = array('odbc', 'pdo', 'pdo_odbc');
                foreach ($extensions as $ext) {
                    $status = extension_loaded($ext) ? '✅ 已加载' : '❌ 未加载';
                    echo $ext . ': ' . $status . "\n";
                }
                ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
