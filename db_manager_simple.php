<?php
/**
 * 简化版数据库管理工具
 * 兼容PHP 5.2+，支持手动配置ADOdb路径
 */

// PHP版本检查
if (version_compare(PHP_VERSION, '5.2.0', '<')) {
    die('此工具需要PHP 5.2.0或更高版本，当前版本: ' . PHP_VERSION);
}

// ===========================================
// 配置区域 - 请根据您的环境修改以下路径
// ===========================================

// ADOdb库路径配置 - 请修改为您的实际路径
$ADODB_PATH = '';  // 留空则自动查找，或手动设置如: '/path/to/adodb/adodb.inc.php'

// 数据库配置
$DB_CONFIGS = array(
    'ucity1' => array(
        'name' => 'Ucity1',
        'user' => 'Program', 
        'password' => '',
        'type' => 'odbc',
        'description' => '站点数据库'
    ),
    'ucitycen' => array(
        'name' => 'ucitycen',
        'user' => 'Program',
        'password' => '',
        'type' => 'odbc', 
        'description' => '中心数据库'
    )
);

// ===========================================
// 自动查找或加载ADOdb库
// ===========================================

if (empty($ADODB_PATH)) {
    // 自动查找ADOdb库
    $search_paths = array(
        'Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php',
        'IncludeFile/adodb/adodb.inc.php',
        'adodb/adodb.inc.php',
        '../adodb/adodb.inc.php',
        '../../adodb/adodb.inc.php',
        'lib/adodb/adodb.inc.php',
        'includes/adodb/adodb.inc.php',
        dirname(__FILE__) . '/adodb/adodb.inc.php',
        dirname(__FILE__) . '/../adodb/adodb.inc.php'
    );
    
    $adodb_found = false;
    foreach ($search_paths as $path) {
        if (file_exists($path)) {
            $ADODB_PATH = $path;
            $adodb_found = true;
            break;
        }
    }
    
    if (!$adodb_found) {
        // 显示配置页面
        showConfigPage($search_paths);
        exit;
    }
}

// 加载ADOdb库
if (!file_exists($ADODB_PATH)) {
    die('错误: ADOdb库文件不存在: ' . htmlspecialchars($ADODB_PATH));
}

require_once($ADODB_PATH);

if (!function_exists('ADONewConnection')) {
    die('错误: ADOdb库加载失败，请检查文件是否正确。');
}

// ===========================================
// 配置页面函数
// ===========================================

function showConfigPage($search_paths) {
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具 - 配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #dc3545; color: white; padding: 2rem; border-radius: 8px; text-align: center; margin-bottom: 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #f8f9fa; padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; font-weight: 600; }
        .card-body { padding: 1.5rem; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 1rem; font-family: 'Consolas', 'Monaco', monospace; font-size: 0.9rem; margin: 1rem 0; }
        .error { color: #dc3545; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 1rem; margin: 1rem 0; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ 配置需要</h1>
            <p>无法找到ADOdb库，需要手动配置</p>
        </div>
        
        <div class="card">
            <div class="card-header">❌ ADOdb库未找到</div>
            <div class="card-body">
                <p>系统在以下路径中查找ADOdb库文件，但都未找到：</p>
                <div class="code">
                <?php foreach ($search_paths as $path): ?>
                    <?= htmlspecialchars($path) ?> - <span class="error">不存在</span><br>
                <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">🔧 解决方案</div>
            <div class="card-body">
                <h4>方案1: 手动设置ADOdb路径</h4>
                <p>编辑 <code><?= basename(__FILE__) ?></code> 文件，找到以下行：</p>
                <div class="code">$ADODB_PATH = '';  // 留空则自动查找</div>
                <p>修改为您的ADOdb库实际路径，例如：</p>
                <div class="code">$ADODB_PATH = '/path/to/your/adodb/adodb.inc.php';</div>
                
                <h4>方案2: 将ADOdb库放到标准位置</h4>
                <p>将ADOdb库文件夹复制到以下任一位置：</p>
                <div class="code">
                    ./adodb/adodb.inc.php<br>
                    ./lib/adodb/adodb.inc.php<br>
                    ./includes/adodb/adodb.inc.php
                </div>
                
                <h4>方案3: 下载ADOdb库</h4>
                <p>如果您没有ADOdb库，可以从官方网站下载：</p>
                <div class="code">
                    官方网站: https://adodb.org/<br>
                    GitHub: https://github.com/ADOdb/ADOdb
                </div>
                
                <div class="warning">
                    <strong>注意:</strong> 根据项目分析，原项目使用的ADOdb库位于 <code>Netask/BrowserUI/MessageCenter/adodb/</code> 目录。
                    请确认该目录是否存在，或者ADOdb库是否已移动到其他位置。
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">📋 当前环境信息</div>
            <div class="card-body">
                <div class="code">
                    PHP版本: <?= PHP_VERSION ?><br>
                    当前目录: <?= htmlspecialchars(getcwd()) ?><br>
                    脚本位置: <?= htmlspecialchars(__FILE__) ?><br>
                    ODBC扩展: <?= extension_loaded('odbc') ? '✅ 已安装' : '❌ 未安装' ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php
}

// ===========================================
// 数据库管理类 (简化版)
// ===========================================

class SimpleDBManager {
    private $connection = null;
    private $currentDb = null;
    
    public function connect($dbKey) {
        global $DB_CONFIGS;
        
        if (!isset($DB_CONFIGS[$dbKey])) {
            throw new Exception("未知的数据库配置: $dbKey");
        }
        
        $config = $DB_CONFIGS[$dbKey];
        
        try {
            $this->connection = ADONewConnection($config['type']);
            $result = $this->connection->PConnect($config['name'], $config['user'], $config['password']);
            
            if (!$result) {
                throw new Exception("数据库连接失败");
            }
            
            $this->connection->SetFetchMode(ADODB_FETCH_ASSOC);
            $this->currentDb = $dbKey;
            
            return true;
        } catch (Exception $e) {
            throw new Exception("连接数据库失败: " . $e->getMessage());
        }
    }
    
    public function getTables() {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        try {
            $tables = $this->connection->MetaTables('TABLES');
            return $tables ? $tables : array();
        } catch (Exception $e) {
            throw new Exception("获取表列表失败: " . $e->getMessage());
        }
    }
    
    public function executeQuery($sql, $limit = 100) {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        $sql = trim($sql);
        if (empty($sql)) {
            throw new Exception("SQL语句不能为空");
        }
        
        // 安全检查
        $upperSql = strtoupper($sql);
        $dangerousKeywords = array('DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE');
        
        foreach ($dangerousKeywords as $keyword) {
            if (strpos($upperSql, $keyword) !== false) {
                throw new Exception("为安全起见，禁止执行 $keyword 操作");
            }
        }
        
        if (!preg_match('/^\s*SELECT\s+/i', $sql)) {
            throw new Exception("只允许执行SELECT查询语句");
        }
        
        try {
            if (stripos($sql, 'LIMIT') === false) {
                $sql .= " LIMIT $limit";
            }
            
            $result = $this->connection->Execute($sql);
            
            if (!$result) {
                throw new Exception("查询执行失败: " . $this->connection->ErrorMsg());
            }
            
            return $result;
        } catch (Exception $e) {
            throw new Exception("查询执行错误: " . $e->getMessage());
        }
    }
    
    public function close() {
        if ($this->connection) {
            $this->connection->Close();
            $this->connection = null;
            $this->currentDb = null;
        }
    }
}

// ===========================================
// 主程序逻辑
// ===========================================

// 处理请求
$action = isset($_GET['action']) ? $_GET['action'] : 'home';
$selectedDb = isset($_GET['db']) ? $_GET['db'] : '';
$error = null;
$result = null;
$tables = null;

$dbManager = new SimpleDBManager();

try {
    if ($selectedDb) {
        $dbManager->connect($selectedDb);
    }
    
    switch ($action) {
        case 'tables':
            $tables = $dbManager->getTables();
            break;
        case 'execute':
            $sql = isset($_POST['sql']) ? $_POST['sql'] : '';
            $result = $dbManager->executeQuery($sql);
            break;
    }
} catch (Exception $e) {
    $error = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版数据库管理工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem 2rem; }
        .header h1 { font-size: 1.8rem; font-weight: 300; }
        .container { max-width: 1200px; margin: 2rem auto; padding: 0 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #f8f9fa; padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; font-weight: 600; }
        .card-body { padding: 1.5rem; }
        .btn { padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 0.25rem; }
        .btn:hover { background: #5a6fd8; text-decoration: none; color: white; }
        .error-alert { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .success-alert { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .db-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; }
        .db-item { border: 2px solid #e9ecef; border-radius: 8px; padding: 1.5rem; text-align: center; text-decoration: none; color: inherit; }
        .db-item:hover { border-color: #667eea; text-decoration: none; color: inherit; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #e9ecef; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .form-control { width: 100%; padding: 0.75rem; border: 1px solid #ced4da; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗄️ 简化版数据库管理工具</h1>
        <p>ADOdb路径: <?= htmlspecialchars($ADODB_PATH) ?></p>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="error-alert">
                <strong>❌ 错误:</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'home'): ?>
            <div class="card">
                <div class="card-header">选择数据库</div>
                <div class="card-body">
                    <div class="db-grid">
                        <?php foreach ($DB_CONFIGS as $dbKey => $config): ?>
                            <a href="?action=tables&db=<?= htmlspecialchars($dbKey) ?>" class="db-item">
                                <h3><?= htmlspecialchars($config['description']) ?></h3>
                                <p>数据库: <?= htmlspecialchars($config['name']) ?></p>
                                <p>类型: <?= htmlspecialchars(strtoupper($config['type'])) ?></p>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
        <?php elseif ($action === 'tables'): ?>
            <div class="card">
                <div class="card-header">
                    数据库表列表
                    <a href="?action=query&db=<?= htmlspecialchars($selectedDb) ?>" class="btn" style="float: right;">SQL查询</a>
                </div>
                <div class="card-body">
                    <?php if ($tables && !empty($tables)): ?>
                        <table class="table">
                            <thead><tr><th>表名</th></tr></thead>
                            <tbody>
                                <?php foreach ($tables as $table): ?>
                                    <tr><td><?= htmlspecialchars($table) ?></td></tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>没有找到表或连接失败</p>
                    <?php endif; ?>
                </div>
            </div>
            
        <?php elseif ($action === 'query' || $action === 'execute'): ?>
            <div class="card">
                <div class="card-header">SQL查询</div>
                <div class="card-body">
                    <form method="POST" action="?action=execute&db=<?= htmlspecialchars($selectedDb) ?>">
                        <textarea class="form-control" name="sql" rows="6" placeholder="输入SELECT查询语句..."><?= htmlspecialchars(isset($_POST['sql']) ? $_POST['sql'] : '') ?></textarea>
                        <br>
                        <button type="submit" class="btn">执行查询</button>
                        <a href="?action=tables&db=<?= htmlspecialchars($selectedDb) ?>" class="btn">返回表列表</a>
                    </form>
                </div>
            </div>
            
            <?php if ($action === 'execute' && $result): ?>
                <div class="success-alert">查询执行成功！</div>
                <div class="card">
                    <div class="card-header">查询结果</div>
                    <div class="card-body">
                        <?php if (!$result->EOF): ?>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <?php $fields = array_keys($result->fields); ?>
                                        <?php foreach ($fields as $field): ?>
                                            <th><?= htmlspecialchars($field) ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $rowCount = 0; ?>
                                    <?php while (!$result->EOF && $rowCount < 50): ?>
                                        <tr>
                                            <?php foreach ($fields as $field): ?>
                                                <td><?= htmlspecialchars($result->fields[$field]) ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                        <?php $result->MoveNext(); $rowCount++; ?>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p>查询无结果</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>

<?php $dbManager->close(); ?>
