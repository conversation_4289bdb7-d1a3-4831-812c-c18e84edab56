<?php
/**
 * 数据库管理工具 - 类似Adminer的PHP数据库管理界面
 * 基于项目现有的数据库连接机制构建
 * 
 * 安全特性：
 * - 防SQL注入
 * - 访问控制
 * - 查询结果限制
 */

// 引入项目的ADOdb库
require_once('Netask/BrowserUI/MessageCenter/adodb/adodb.inc.php');

// 数据库配置 - 复用项目配置
class DatabaseConfig {
    public static $databases = [
        'ucity1' => [
            'name' => 'Ucity1',
            'user' => 'Program', 
            'password' => '',
            'type' => 'odbc',
            'description' => '站点数据库'
        ],
        'ucitycen' => [
            'name' => 'ucitycen',
            'user' => 'Program',
            'password' => '',
            'type' => 'odbc', 
            'description' => '中心数据库'
        ]
    ];
}

// 数据库连接管理类
class DatabaseManager {
    private $connection = null;
    private $currentDb = null;
    
    /**
     * 连接数据库 - 复用项目的连接方式
     */
    public function connect($dbKey) {
        if (!isset(DatabaseConfig::$databases[$dbKey])) {
            throw new Exception("未知的数据库配置: $dbKey");
        }
        
        $config = DatabaseConfig::$databases[$dbKey];
        
        try {
            $this->connection = ADONewConnection($config['type']);
            $result = $this->connection->PConnect($config['name'], $config['user'], $config['password']);
            
            if (!$result) {
                throw new Exception("数据库连接失败");
            }
            
            $this->connection->SetFetchMode(ADODB_FETCH_ASSOC);
            $this->currentDb = $dbKey;
            
            return true;
        } catch (Exception $e) {
            throw new Exception("连接数据库失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取数据库列表
     */
    public function getDatabases() {
        return array_keys(DatabaseConfig::$databases);
    }
    
    /**
     * 获取表列表
     */
    public function getTables() {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        try {
            $tables = $this->connection->MetaTables('TABLES');
            return $tables ? $tables : [];
        } catch (Exception $e) {
            throw new Exception("获取表列表失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取表结构
     */
    public function getTableStructure($tableName) {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }
        
        // 防止SQL注入 - 验证表名
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        
        try {
            $columns = $this->connection->MetaColumns($tableName);
            return $columns ? $columns : [];
        } catch (Exception $e) {
            throw new Exception("获取表结构失败: " . $e->getMessage());
        }
    }
    
    /**
     * 执行查询 - 带安全检查
     */
    public function executeQuery($sql, $limit = 100) {
        if (!$this->connection) {
            throw new Exception("未连接数据库");
        }

        // 基本的SQL安全检查
        $sql = trim($sql);
        if (empty($sql)) {
            throw new Exception("SQL语句不能为空");
        }

        // 更严格的安全检查
        $this->validateSqlSecurity($sql);

        try {
            // 限制查询结果数量
            if (stripos($sql, 'LIMIT') === false) {
                $sql .= " LIMIT $limit";
            }

            $result = $this->connection->Execute($sql);

            if (!$result) {
                throw new Exception("查询执行失败: " . $this->connection->ErrorMsg());
            }

            return $result;
        } catch (Exception $e) {
            throw new Exception("查询执行错误: " . $e->getMessage());
        }
    }

    /**
     * 验证SQL安全性
     */
    private function validateSqlSecurity($sql) {
        $upperSql = strtoupper($sql);

        // 禁止危险操作
        $dangerousKeywords = [
            'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE',
            'EXEC', 'EXECUTE', 'CALL', 'DECLARE', 'SET', 'USE', 'GRANT', 'REVOKE'
        ];

        foreach ($dangerousKeywords as $keyword) {
            if (strpos($upperSql, $keyword) !== false) {
                throw new Exception("为安全起见，禁止执行 $keyword 操作");
            }
        }

        // 检查是否只包含SELECT语句
        if (!preg_match('/^\s*SELECT\s+/i', $sql)) {
            throw new Exception("只允许执行SELECT查询语句");
        }

        // 禁止多语句执行
        if (strpos($sql, ';') !== false && strpos($sql, ';') < strlen($sql) - 1) {
            throw new Exception("不允许执行多条SQL语句");
        }

        // 检查潜在的注入模式
        $injectionPatterns = [
            '/union\s+select/i',
            '/\'\s*or\s*\'/i',
            '/\'\s*and\s*\'/i',
            '/--\s*$/m',
            '/\/\*.*?\*\//s'
        ];

        foreach ($injectionPatterns as $pattern) {
            if (preg_match($pattern, $sql)) {
                throw new Exception("检测到潜在的SQL注入模式，查询被拒绝");
            }
        }
    }
    
    /**
     * 获取表数据
     */
    public function getTableData($tableName, $limit = 50, $offset = 0) {
        // 防止SQL注入 - 验证表名
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName)) {
            throw new Exception("无效的表名");
        }
        
        $sql = "SELECT * FROM $tableName LIMIT $limit OFFSET $offset";
        return $this->executeQuery($sql, $limit);
    }
    
    /**
     * 关闭连接
     */
    public function close() {
        if ($this->connection) {
            $this->connection->Close();
            $this->connection = null;
            $this->currentDb = null;
        }
    }
    
    /**
     * 获取当前数据库
     */
    public function getCurrentDatabase() {
        return $this->currentDb;
    }
}

// 主应用类
class DatabaseAdmin {
    private $dbManager;
    private $action;
    private $selectedDb;
    private $selectedTable;
    
    public function __construct() {
        $this->dbManager = new DatabaseManager();
        $this->action = $_GET['action'] ?? 'home';
        $this->selectedDb = $_GET['db'] ?? '';
        $this->selectedTable = $_GET['table'] ?? '';
    }
    
    /**
     * 运行应用
     */
    public function run() {
        try {
            // 如果选择了数据库，尝试连接
            if ($this->selectedDb) {
                $this->dbManager->connect($this->selectedDb);
            }
            
            // 处理不同的操作
            switch ($this->action) {
                case 'tables':
                    $this->showTables();
                    break;
                case 'structure':
                    $this->showTableStructure();
                    break;
                case 'browse':
                    $this->browseTable();
                    break;
                case 'query':
                    $this->showQueryInterface();
                    break;
                case 'execute':
                    $this->executeQuery();
                    break;
                default:
                    $this->showHome();
            }
        } catch (Exception $e) {
            $this->showError($e->getMessage());
        }
    }
    
    /**
     * 显示首页
     */
    private function showHome() {
        $databases = $this->dbManager->getDatabases();
        include 'db_admin_home.php';
    }
    
    /**
     * 显示表列表
     */
    private function showTables() {
        $tables = $this->dbManager->getTables();
        include 'db_admin_tables.php';
    }
    
    /**
     * 显示表结构
     */
    private function showTableStructure() {
        $structure = $this->dbManager->getTableStructure($this->selectedTable);
        include 'db_admin_structure.php';
    }
    
    /**
     * 浏览表数据
     */
    private function browseTable() {
        $data = $this->dbManager->getTableData($this->selectedTable);
        include 'db_admin_browse.php';
    }
    
    /**
     * 显示查询界面
     */
    private function showQueryInterface() {
        include 'db_admin_query.php';
    }
    
    /**
     * 执行查询
     */
    private function executeQuery() {
        $sql = $_POST['sql'] ?? '';
        $result = $this->dbManager->executeQuery($sql);
        include 'db_admin_result.php';
    }
    
    /**
     * 显示错误
     */
    private function showError($message) {
        include 'db_admin_error.php';
    }
    
    /**
     * 析构函数 - 确保连接关闭
     */
    public function __destruct() {
        $this->dbManager->close();
    }
}

// 如果直接访问此文件，运行应用
if (basename($_SERVER['PHP_SELF']) == 'db_admin.php') {
    $app = new DatabaseAdmin();
    $app->run();
}
?>
