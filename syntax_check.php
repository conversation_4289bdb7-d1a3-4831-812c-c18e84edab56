<?php
/**
 * PHP 5.2 语法兼容性检查工具
 */

// PHP版本检查
if (version_compare(PHP_VERSION, '5.2.0', '<')) {
    die('此检查工具需要PHP 5.2.0或更高版本，当前版本: ' . PHP_VERSION);
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP 5.2 语法兼容性检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #667eea; color: white; padding: 2rem; border-radius: 8px; text-align: center; margin-bottom: 2rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1rem; overflow: hidden; }
        .card-header { padding: 1rem 1.5rem; font-weight: 600; border-bottom: 1px solid #e9ecef; }
        .card-body { padding: 1.5rem; }
        .success { border-left: 4px solid #28a745; }
        .success .card-header { background: #d4edda; color: #155724; }
        .error { border-left: 4px solid #dc3545; }
        .error .card-header { background: #f8d7da; color: #721c24; }
        .warning { border-left: 4px solid #ffc107; }
        .warning .card-header { background: #fff3cd; color: #856404; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 1rem; font-family: 'Consolas', 'Monaco', monospace; font-size: 0.9rem; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: #667eea; color: white; text-decoration: none; border-radius: 4px; margin-top: 1rem; }
        .btn:hover { background: #5a6fd8; text-decoration: none; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 PHP 5.2 语法兼容性检查</h1>
            <p>检查数据库管理工具的PHP 5.2兼容性</p>
        </div>
        
        <?php
        // 检查PHP版本
        echo '<div class="card success">';
        echo '<div class="card-header">✅ PHP版本检查</div>';
        echo '<div class="card-body">';
        echo '<p><strong>当前PHP版本:</strong> ' . PHP_VERSION . '</p>';
        echo '<p><strong>兼容性:</strong> ' . (version_compare(PHP_VERSION, '5.2.0', '>=') ? '✅ 兼容' : '❌ 不兼容') . '</p>';
        echo '</div></div>';
        
        // 检查必需的扩展
        $required_extensions = array('odbc', 'pcre', 'session');
        $missing_extensions = array();
        
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                $missing_extensions[] = $ext;
            }
        }
        
        if (empty($missing_extensions)) {
            echo '<div class="card success">';
            echo '<div class="card-header">✅ PHP扩展检查</div>';
            echo '<div class="card-body">';
            echo '<p>所有必需的PHP扩展都已安装：</p>';
            echo '<div class="code">';
            foreach ($required_extensions as $ext) {
                echo $ext . ': ✅ 已安装<br>';
            }
            echo '</div>';
            echo '</div></div>';
        } else {
            echo '<div class="card error">';
            echo '<div class="card-header">❌ PHP扩展检查失败</div>';
            echo '<div class="card-body">';
            echo '<p>以下扩展缺失：</p>';
            echo '<div class="code">';
            foreach ($missing_extensions as $ext) {
                echo $ext . ': ❌ 未安装<br>';
            }
            echo '</div>';
            echo '</div></div>';
        }
        
        // 语法检查
        $files_to_check = array('db_manager.php');
        $syntax_errors = array();
        
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                // 使用php -l 检查语法
                $output = array();
                $return_code = 0;
                exec("php -l \"$file\" 2>&1", $output, $return_code);
                
                if ($return_code !== 0) {
                    $syntax_errors[$file] = implode("\n", $output);
                }
            } else {
                $syntax_errors[$file] = "文件不存在";
            }
        }
        
        if (empty($syntax_errors)) {
            echo '<div class="card success">';
            echo '<div class="card-header">✅ 语法检查通过</div>';
            echo '<div class="card-body">';
            echo '<p>所有文件的PHP语法都正确：</p>';
            echo '<div class="code">';
            foreach ($files_to_check as $file) {
                echo $file . ': ✅ 语法正确<br>';
            }
            echo '</div>';
            echo '</div></div>';
        } else {
            echo '<div class="card error">';
            echo '<div class="card-header">❌ 语法检查失败</div>';
            echo '<div class="card-body">';
            echo '<p>发现语法错误：</p>';
            foreach ($syntax_errors as $file => $error) {
                echo '<div class="code">';
                echo '<strong>' . htmlspecialchars($file) . ':</strong><br>';
                echo htmlspecialchars($error);
                echo '</div><br>';
            }
            echo '</div></div>';
        }
        
        // 功能测试
        echo '<div class="card warning">';
        echo '<div class="card-header">⚠️ 功能测试建议</div>';
        echo '<div class="card-body">';
        echo '<p>语法检查通过后，建议进行以下功能测试：</p>';
        echo '<ol>';
        echo '<li>数据库连接测试</li>';
        echo '<li>表列表获取测试</li>';
        echo '<li>表结构查看测试</li>';
        echo '<li>数据浏览测试</li>';
        echo '<li>SQL查询执行测试</li>';
        echo '</ol>';
        echo '<a href="test_db_connection.php" class="btn">🔗 运行连接测试</a>';
        echo '<a href="db_manager.php" class="btn">🚀 启动数据库管理工具</a>';
        echo '</div></div>';
        
        // PHP 5.2 特有的注意事项
        echo '<div class="card warning">';
        echo '<div class="card-header">💡 PHP 5.2 注意事项</div>';
        echo '<div class="card-body">';
        echo '<h4>已修复的兼容性问题：</h4>';
        echo '<ul>';
        echo '<li>✅ 短数组语法 [] 改为 array()</li>';
        echo '<li>✅ 空合并操作符 ?? 改为三元操作符</li>';
        echo '<li>✅ 静态数组属性改为静态方法</li>';
        echo '<li>✅ 添加PHP版本检查</li>';
        echo '</ul>';
        
        echo '<h4>性能优化建议：</h4>';
        echo '<ul>';
        echo '<li>启用OPcache（如果可用）</li>';
        echo '<li>适当设置memory_limit</li>';
        echo '<li>设置合理的max_execution_time</li>';
        echo '<li>使用持久数据库连接</li>';
        echo '</ul>';
        echo '</div></div>';
        ?>
        
        <div class="card">
            <div class="card-header">📋 检查完成</div>
            <div class="card-body">
                <p>PHP 5.2兼容性检查已完成。如果所有检查都通过，您可以安全地使用数据库管理工具。</p>
                <p><strong>建议：</strong>虽然工具支持PHP 5.2，但为了更好的性能和安全性，建议升级到PHP 7.4或更高版本。</p>
            </div>
        </div>
    </div>
</body>
</html>
